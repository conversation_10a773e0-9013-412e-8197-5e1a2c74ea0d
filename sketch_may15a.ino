#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>

// WiFi credentials - CHANGE THESE TO YOUR NETWORK
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

WebServer server(80);

const int ROWS = 4;
const int COLS = 5;

// Control parameters
int onTime, offTime, duration = 8000, divider = 5;
float ratioOnOff = 0.75;
int isDrawing = 0, startDrawing = 0;
int selectedShapeIndex = 0; // 0=Rectangle, 1=Circle, etc.


// --------------------------------- Matrix ---------------------------------
int M[ROWS][COLS] = {
  { 15, 2, 0, 4, 16 },    // { GPIO15, GPIO2, GPIO0, GPIO4, GP<PERSON>16 },
  { 17, 5, 18, 19, 21 },  // { GP<PERSON>17, <PERSON><PERSON>5, <PERSON><PERSON>18, <PERSON><PERSON>19, <PERSON><PERSON>21 },
  { 22, 23, 13, 32, 33 }, // { GPIO22, GP<PERSON>23, GPIO13, GP<PERSON>32, GPIO33 },
  { 25, 26, 27, 14, 12 }  // { GPIO25, GPIO26, GPIO27, GPIO14, GPIO12 }
};


// --------------------------------- Shapes ---------------------------------
const int shapeRectangle[ROWS][COLS] = {
  { 1, 1, 1, 1, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 1, 1, 1, 1 }
};
const int shapeCircle[ROWS][COLS] = {
  { 0, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeOpenCircle[ROWS][COLS] = {
  { 0, 1, 1, 1, 0 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeTriangle[ROWS][COLS] = {
  { 0, 0, 1, 0, 0 },
  { 0, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 0, 0, 0, 0, 0 }
};
const int shapeVLine[ROWS][COLS] = {
  { 0, 0, 0, 0, 0 },
  { 1, 1, 1, 1, 1 },
  { 0, 0, 0, 0, 0 },
  { 0, 0, 0, 0, 0 }
};
const int shapeHLine[ROWS][COLS] = {
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 }
};
const int shapeDiagonal[ROWS][COLS] = {
  { 1, 0, 0, 0, 0 },
  { 0, 1, 0, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 0, 1, 0 }
};
const int shapeCross[ROWS][COLS] = {
  { 1, 0, 0, 1, 0 },
  { 0, 1, 1, 0, 0 },
  { 0, 1, 1, 0, 0 },
  { 1, 0, 0, 1, 0 }
};
const int shapeU[ROWS][COLS] = {
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeCup[ROWS][COLS] = {
  { 1, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 0 }
};

// Shape names for web interface
const char* shapeNames[] = {
  "Rectangle", "Circle", "Open Circle", "Triangle",
  "Vertical Line", "Horizontal Line", "Diagonal", "Cross", "U", "Cup"
};

// Array of shape pointers for easy access
const int (*shapes[10])[COLS] = {
  shapeRectangle, shapeCircle, shapeOpenCircle, shapeTriangle,
  shapeVLine, shapeHLine, shapeDiagonal, shapeCross, shapeU, shapeCup
};


// --------------------------------- Display it ---------------------------------
void drawShape(const int shape[ROWS][COLS]) {
  for (int row = 0; row < ROWS; row++) {
    for (int col = 0; col < COLS; col++) {
      digitalWrite(M[row][col], shape[row][col] == 1 ? HIGH : LOW);
    }
  }
}

// --------------------------------- Clear it ---------------------------------
void clearM() {
  for (int row = 0; row < ROWS; row++) {
    for (int col = 0; col < COLS; col++) {
      digitalWrite(M[row][col], LOW);
    }
  }
}

// --------------------------------- Web Server Functions ---------------------------------
void handleRoot() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Thermal Tactile Display</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .control-group {
            margin-bottom: 25px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }
        input[type="range"] {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        select, input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        select:focus, input[type="number"]:focus {
            border-color: #667eea;
            outline: none;
        }
        .value-display {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        .button-group {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin: 0 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
        }
        .status.idle {
            background: #c6f6d5;
            color: #22543d;
        }
        .status.drawing {
            background: #fed7d7;
            color: #742a2a;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 600px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Thermal Tactile Display</h1>

        <div id="status" class="status idle">Status: Idle</div>

        <div class="grid">
            <div class="control-group">
                <label for="duration">Duration (ms): <span id="durationValue" class="value-display">8000</span></label>
                <input type="range" id="duration" min="1000" max="20000" value="8000" step="100">
            </div>

            <div class="control-group">
                <label for="ratio">On/Off Ratio: <span id="ratioValue" class="value-display">0.75</span></label>
                <input type="range" id="ratio" min="0.1" max="1.0" value="0.75" step="0.05">
            </div>

            <div class="control-group">
                <label for="divider">Divider: <span id="dividerValue" class="value-display">5</span></label>
                <input type="range" id="divider" min="1" max="20" value="5" step="1">
            </div>

            <div class="control-group">
                <label for="shape">Shape:</label>
                <select id="shape">
                    <option value="0">Rectangle</option>
                    <option value="1">Circle</option>
                    <option value="2">Open Circle</option>
                    <option value="3">Triangle</option>
                    <option value="4">Vertical Line</option>
                    <option value="5">Horizontal Line</option>
                    <option value="6">Diagonal</option>
                    <option value="7">Cross</option>
                    <option value="8">U</option>
                    <option value="9">Cup</option>
                </select>
            </div>
        </div>

        <div class="button-group">
            <button id="startBtn" onclick="startDrawing()">🚀 Start Drawing</button>
            <button onclick="updateParameters()">💾 Update Parameters</button>
            <button onclick="refreshStatus()">🔄 Refresh Status</button>
        </div>
    </div>

    <script>
        // Update value displays
        document.getElementById('duration').oninput = function() {
            document.getElementById('durationValue').textContent = this.value;
        }
        document.getElementById('ratio').oninput = function() {
            document.getElementById('ratioValue').textContent = this.value;
        }
        document.getElementById('divider').oninput = function() {
            document.getElementById('dividerValue').textContent = this.value;
        }

        function updateParameters() {
            const params = {
                duration: parseInt(document.getElementById('duration').value),
                ratio: parseFloat(document.getElementById('ratio').value),
                divider: parseInt(document.getElementById('divider').value),
                shape: parseInt(document.getElementById('shape').value)
            };

            fetch('/setParams', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(params)
            })
            .then(response => response.json())
            .then(data => {
                if(data.success) {
                    alert('Parameters updated successfully!');
                } else {
                    alert('Error updating parameters');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating parameters');
            });
        }

        function startDrawing() {
            document.getElementById('startBtn').disabled = true;
            fetch('/start', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if(data.success) {
                    refreshStatus();
                } else {
                    alert('Error starting drawing');
                    document.getElementById('startBtn').disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error starting drawing');
                document.getElementById('startBtn').disabled = false;
            });
        }

        function refreshStatus() {
            fetch('/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('status');
                const startBtn = document.getElementById('startBtn');

                if(data.isDrawing) {
                    statusDiv.textContent = 'Status: Drawing...';
                    statusDiv.className = 'status drawing';
                    startBtn.disabled = true;
                } else {
                    statusDiv.textContent = 'Status: Idle';
                    statusDiv.className = 'status idle';
                    startBtn.disabled = false;
                }

                // Update current parameters
                document.getElementById('duration').value = data.duration;
                document.getElementById('durationValue').textContent = data.duration;
                document.getElementById('ratio').value = data.ratio;
                document.getElementById('ratioValue').textContent = data.ratio;
                document.getElementById('divider').value = data.divider;
                document.getElementById('dividerValue').textContent = data.divider;
                document.getElementById('shape').value = data.shape;
            })
            .catch(error => console.error('Error:', error));
        }

        // Auto-refresh status every 2 seconds
        setInterval(refreshStatus, 2000);

        // Initial status load
        refreshStatus();
    </script>
</body>
</html>
)";
  server.send(200, "text/html", html);
}

void handleSetParams() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(1024);
    deserializeJson(doc, server.arg("plain"));

    duration = doc["duration"];
    ratioOnOff = doc["ratio"];
    divider = doc["divider"];
    selectedShapeIndex = doc["shape"];

    server.send(200, "application/json", "{\"success\":true}");
    Serial.println("Parameters updated via web interface");
  } else {
    server.send(400, "application/json", "{\"success\":false,\"error\":\"No data\"}");
  }
}

void handleStart() {
  if (isDrawing == 0) {
    startDrawing = 1;
    server.send(200, "application/json", "{\"success\":true}");
    Serial.println("Drawing started via web interface");
  } else {
    server.send(200, "application/json", "{\"success\":false,\"error\":\"Already drawing\"}");
  }
}

void handleStatus() {
  String json = "{";
  json += "\"isDrawing\":" + String(isDrawing) + ",";
  json += "\"duration\":" + String(duration) + ",";
  json += "\"ratio\":" + String(ratioOnOff, 2) + ",";
  json += "\"divider\":" + String(divider) + ",";
  json += "\"shape\":" + String(selectedShapeIndex);
  json += "}";

  server.send(200, "application/json", json);
}

void setupWiFi() {
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");

  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }

  Serial.println();
  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
}

void setupWebServer() {
  server.on("/", handleRoot);
  server.on("/setParams", HTTP_POST, handleSetParams);
  server.on("/start", HTTP_POST, handleStart);
  server.on("/status", HTTP_GET, handleStatus);

  server.begin();
  Serial.println("Web server started");
}




void setup() {
  Serial.begin(115200); // Initialize Serial for debugging (IF you are not using GPIO1/3)
  delay(1000); // Give serial a moment to connect

  Serial.println("Setting up GPIO pins as OUTPUT...");

  for (int r = 0; r < ROWS; r++) {
    for (int c = 0; c < COLS; c++) {
      int currentPin = M[r][c];
      // INFO: Pin GPIO%d is a strapping pin. Ensure it's not held LOW at boot by external circuitry if you don't want bootloader mode.
      pinMode(currentPin, OUTPUT);
      digitalWrite(currentPin, LOW); // Optional: set an initial state (e.g., LOW)
    }
  }

  Serial.println("GPIO setup complete.");

  // Initialize WiFi and Web Server
  setupWiFi();
  setupWebServer();

  Serial.println("System ready!");
  Serial.print("Access the web interface at: http://");
  Serial.println(WiFi.localIP());
}

void loop() {
  // Handle web server requests
  server.handleClient();

  // ---------------------------------------------- Calculations ----------------------------------------------
  int adjustedDuration = duration / divider;
  onTime = adjustedDuration * ratioOnOff;
  offTime = adjustedDuration - onTime;

  // ------------------------------------------- Inputs and control -------------------------------------------
  const int (*choosenShape)[COLS] = shapes[selectedShapeIndex]; // Shape selected from web interface

  // ------------------------------------------------- Drawing ------------------------------------------------
  if (startDrawing == 1) {
    isDrawing = 1; // Indicate that it is drawing (not input)
    Serial.print("Drawing shape: ");
    Serial.print(shapeNames[selectedShapeIndex]);
    Serial.print(" with duration: ");
    Serial.print(duration);
    Serial.print(", ratio: ");
    Serial.print(ratioOnOff);
    Serial.print(", divider: ");
    Serial.println(divider);

    for (int i = 0; i < divider; i++) {
      drawShape(choosenShape);
      delay(onTime);
      clearM();
      delay(offTime);
    }
    isDrawing = 0;
    startDrawing = 0;
    Serial.println("Drawing complete");
  }

  delay(10); // Small delay to prevent watchdog issues
}
